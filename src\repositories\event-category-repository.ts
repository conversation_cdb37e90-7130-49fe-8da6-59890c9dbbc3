import { BaseRepository } from '@/lib/db/base-repository';
import { z } from 'zod';
import { createClient } from '@/lib/supabase/pages-client';
import {
  EventCategory,
  BaseEventCategory,
  RunningEventCategory,
  ConferenceEventCategory,
  eventCategorySchema,
  baseEventCategorySchema,
  runningEventCategoryPropertiesSchema,
  conferenceEventCategoryPropertiesSchema
} from '@/types/event-types';

/**
 * Event Category database schema for the repository
 */
const EventCategoryDBSchema = z.object({
  id: z.string().uuid(),
  event_id: z.string().uuid(),
  name: z.string(),
  description: z.string().nullable(),
  // JSON-based properties field
  properties: z.record(z.any()).nullable(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

/**
 * Type for the EventCategory database model
 */
type EventCategoryDB = z.infer<typeof EventCategoryDBSchema>;

/**
 * Repository for event category operations
 */
export class EventCategoryRepository extends BaseRepository<EventCategoryDB> {
  constructor() {
    super('event_categories', EventCategoryDBSchema);
  }

  /**
   * Transform the database event category to the application event category
   */
  private toEventCategory(dbCategory: EventCategoryDB): EventCategory {
    // Log the raw database category for debugging
    console.log(`[EventCategoryRepository] Converting DB category to app category:`, {
      id: dbCategory.id,
      name: dbCategory.name,
      rawProperties: dbCategory.properties
    });

    // Create a base category with properties
    const baseCategory: BaseEventCategory = {
      id: dbCategory.id,
      eventId: dbCategory.event_id,
      name: dbCategory.name,
      description: dbCategory.description || "",
      properties: dbCategory.properties || {},
      createdAt: new Date(dbCategory.created_at),
      updatedAt: new Date(dbCategory.updated_at),
    };

    // Log the converted category for debugging
    console.log(`[EventCategoryRepository] Converted category properties:`, {
      id: baseCategory.id,
      name: baseCategory.name,
      price: baseCategory.properties?.price,
      startTime: baseCategory.properties?.startTime,
      earlyBirdPrice: baseCategory.properties?.earlyBirdPrice,
      earlyBirdEndDate: baseCategory.properties?.earlyBirdEndDate,
    });

    // Return the base category with properties
    return baseCategory;
  }

  /**
   * Transform the database event category to a running event category
   */
  private toRunningEventCategory(dbCategory: EventCategoryDB): RunningEventCategory {
    // Extract running-specific properties
    const properties = dbCategory.properties || {};

    return {
      id: dbCategory.id,
      eventId: dbCategory.event_id,
      name: dbCategory.name,
      description: dbCategory.description || "",
      properties: properties as Record<string, unknown>,
      createdAt: new Date(dbCategory.created_at),
      updatedAt: new Date(dbCategory.updated_at),
    };
  }

  /**
   * Transform the database event category to a conference event category
   */
  private toConferenceEventCategory(dbCategory: EventCategoryDB): ConferenceEventCategory {
    // Extract conference-specific properties
    const properties = dbCategory.properties || {};

    return {
      id: dbCategory.id,
      eventId: dbCategory.event_id,
      name: dbCategory.name,
      description: dbCategory.description || "",
      properties: properties as Record<string, unknown>,
      createdAt: new Date(dbCategory.created_at),
      updatedAt: new Date(dbCategory.updated_at),
    };
  }

  /**
   * Transform the application event category to the database event category
   */
  private toEventCategoryDB(category: Partial<EventCategory> | Partial<BaseEventCategory>): Partial<EventCategoryDB> {
    const result: Partial<EventCategoryDB> = {};

    // Handle base fields
    if ('eventId' in category && category.eventId !== undefined) result.event_id = category.eventId;
    if ('name' in category && category.name !== undefined) result.name = category.name;
    if ('description' in category && category.description !== undefined) result.description = category.description || null;
    else if ('description' in category) result.description = null;

    // Handle properties field
    if ('properties' in category && category.properties !== undefined) {
      result.properties = category.properties;
    }

    // No legacy fields to handle

    return result;
  }

  /**
   * Get all categories for an event
   */
  async getCategoriesByEventId(eventId: string): Promise<EventCategory[]> {
    console.log(`[EventCategoryRepository] Getting categories for event: ${eventId}`);

    const result = await this.find({ event_id: eventId });

    if (!result.success) {
      console.error(`[EventCategoryRepository] Failed to get categories: ${result.message}`);
      throw new Error(`Failed to get event categories: ${result.message}`);
    }

    const categories = (result.data || []).map((item) => this.toEventCategory(item));

    // Log the retrieved categories with detailed properties
    console.log(`[EventCategoryRepository] Retrieved ${categories.length} categories for event ${eventId}`);
    categories.forEach((category, index) => {
      console.log(`[EventCategoryRepository] Category ${index + 1}: ${category.name}`, {
        id: category.id,
        properties: {
          price: category.properties?.price,
          startTime: category.properties?.startTime,
          earlyBirdPrice: category.properties?.earlyBirdPrice,
          earlyBirdEndDate: category.properties?.earlyBirdEndDate,
          registrationLimit: category.properties?.registrationLimit,
          registrationCloseDate: category.properties?.registrationCloseDate,
        }
      });
    });

    return categories;
  }

  /**
   * Get category by ID
   */
  async getCategoryById(id: string): Promise<EventCategory | null> {
    const result = await this.findById(id);

    if (!result.success) {
      if (result.message?.includes('not found')) {
        return null;
      }
      throw new Error(`Failed to get event category: ${result.message}`);
    }

    return this.toEventCategory(result.data!);
  }

  /**
   * Create a new category or update if it already exists
   */
  async createCategory(
    category: Omit<EventCategory, 'id' | 'createdAt' | 'updatedAt'> |
      (Omit<BaseEventCategory, 'id' | 'createdAt' | 'updatedAt'> & { eventType?: string })
  ): Promise<EventCategory> {
    // Log the incoming category data with more details
    console.log('[EventCategoryRepository] Creating category:', {
      name: category.name,
      eventId: category.eventId,
      hasProperties: 'properties' in category,
      isLegacy: 'bibRequireGeneration' in category || 'registrationOpen' in category,
      properties: 'properties' in category ? {
        price: category.properties?.price,
        startTime: category.properties?.startTime,
        earlyBirdPrice: category.properties?.earlyBirdPrice,
        earlyBirdEndDate: category.properties?.earlyBirdEndDate,
        registrationLimit: category.properties?.registrationLimit,
        registrationCloseDate: category.properties?.registrationCloseDate,
      } : 'No properties'
    });

    // Validate required fields
    if (!category.eventId) {
      console.error('[EventCategoryRepository] Missing eventId in category:', category.name);
      throw new Error('eventId is required for creating a category');
    }

    if (!category.name) {
      console.error('[EventCategoryRepository] Missing name in category');
      throw new Error('name is required for creating a category');
    }

    try {
      // Validate with the base schema
      console.log('[EventCategoryRepository] Validating with base schema');
      try {
        baseEventCategorySchema.parse(category);
      } catch (validationError) {
        console.error('[EventCategoryRepository] Base schema validation error:', validationError);
        // Continue despite validation errors for draft saving
      }

      // If event type is provided, validate properties based on event type
      if ('eventType' in category && category.eventType) {
        console.log(`[EventCategoryRepository] Validating properties for event type: ${category.eventType}`);
        try {
          switch (category.eventType) {
            case 'running':
              runningEventCategoryPropertiesSchema.parse(category.properties);
              break;
            case 'conference':
              conferenceEventCategoryPropertiesSchema.parse(category.properties);
              break;
          }
        } catch (validationError) {
          console.error(`[EventCategoryRepository] Event type properties validation error:`, validationError);
          // Continue despite validation errors for draft saving
        }
      }
    } catch (error) {
      console.warn('[EventCategoryRepository] Category validation warning (continuing anyway):', error);
      // Continue despite validation errors for draft saving
    }

    // Check if a category with the same name already exists for this event
    try {
      console.log(`[EventCategoryRepository] Checking if category '${category.name}' already exists for event ${category.eventId}`);
      const supabase = await createClient();
      const { data: existingCategories, error } = await supabase
        .from('event_categories')
        .select('*')
        .eq('event_id', category.eventId)
        .eq('name', category.name);

      if (error) {
        console.error('[EventCategoryRepository] Error checking for existing category:', error);
      } else if (existingCategories && existingCategories.length > 0) {
        // Category with the same name already exists for this event, update it instead
        const existingCategory = existingCategories[0];
        if (!existingCategory) {
          throw new Error('Existing category not found despite being in the array');
        }

        console.log(`[EventCategoryRepository] Found existing category with name '${category.name}' for event ${category.eventId}:`, {
          id: existingCategory.id,
          name: existingCategory.name
        });

        // Update the existing category
        return this.updateCategory(existingCategory.id, {
          description: category.description || "",
          properties: 'properties' in category ? category.properties : {}
        });
      }
    } catch (error) {
      console.error('[EventCategoryRepository] Error checking for existing category:', error);
      // Continue with creation even if the check fails
    }

    // Prepare the database record for a new category
    const dbCategory: EventCategoryDB = {
      id: crypto.randomUUID(),
      event_id: category.eventId,
      name: category.name,
      description: category.description || null,
      // Initialize properties field
      properties: {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Use properties field
    const baseCategory = category as Omit<BaseEventCategory, 'id' | 'createdAt' | 'updatedAt'>;

    // Add detailed logging for properties before assignment
    if (baseCategory.properties) {
      console.log('[EventCategoryRepository] Properties before assignment:', baseCategory.properties);

      // Check registration limit specifically
      if (baseCategory.properties.registrationLimit !== undefined) {
        console.log('[EventCategoryRepository] Registration limit in createCategory:',
          baseCategory.properties.registrationLimit,
          'type:', typeof baseCategory.properties.registrationLimit);

        // IMPORTANT: Keep the original value without any conversion
        // This preserves the exact number the user entered
        const originalValue = baseCategory.properties.registrationLimit;

        // If it's a string (which it shouldn't be at this point), we'll convert it
        // But we won't convert numbers to avoid any precision issues
        if (typeof originalValue === 'string') {
          console.log('[EventCategoryRepository] Converting string value to number in createCategory');
          baseCategory.properties.registrationLimit = Number(originalValue);
        }

        console.log('[EventCategoryRepository] Final registration limit value in createCategory:',
          baseCategory.properties.registrationLimit,
          'type:', typeof baseCategory.properties.registrationLimit);
      }
    }

    dbCategory.properties = baseCategory.properties || {};

    // Log the prepared database record before creating
    console.log('[EventCategoryRepository] Prepared database record:', {
      id: dbCategory.id,
      event_id: dbCategory.event_id,
      name: dbCategory.name,
      hasProperties: dbCategory.properties && Object.keys(dbCategory.properties).length > 0
    });

    try {
      const result = await this.create(dbCategory);

      if (!result.success) {
        console.error('[EventCategoryRepository] Failed to create category:', result.message);
        throw new Error(`Failed to create event category: ${result.message}`);
      }

      console.log('[EventCategoryRepository] Successfully created category:', {
        id: result.data?.id,
        name: result.data?.name
      });

      return this.toEventCategory(result.data!);
    } catch (error) {
      console.error('[EventCategoryRepository] Exception during category creation:', error);
      throw error;
    }
  }

  /**
   * Update a category
   */
  async updateCategory(
    id: string,
    category: Partial<Omit<EventCategory, 'id' | 'createdAt' | 'updatedAt'>> |
      Partial<Omit<BaseEventCategory, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<EventCategory> {
    // Get the existing category to determine if we need to merge properties
    const existingCategory = await this.getCategoryById(id);

    if (!existingCategory) {
      throw new Error(`Category not found with id: ${id}`);
    }

    // Prepare the data to update
    const dbCategory = this.toEventCategoryDB(category);

    // If updating properties, merge with existing properties
    if ('properties' in category && category.properties) {
      // Get existing properties from the database
      const { data: existing } = await this.findById(id);

      if (existing) {
        // Create a copy of the existing properties
        const existingProps = { ...(existing.properties || {}) };

        // Create a copy of the new properties and ensure numeric values are numbers
        const newProps = { ...(category.properties || {}) };

        // Convert numeric values to numbers
        if (newProps.price !== undefined) newProps.price = Number(newProps.price);
        if (newProps.earlyBirdPrice !== undefined) newProps.earlyBirdPrice = Number(newProps.earlyBirdPrice);
        if (newProps.capacity !== undefined) newProps.capacity = Number(newProps.capacity);

        // Add detailed logging for registration limit
        if (newProps.registrationLimit !== undefined) {
          console.log('[EventCategoryRepository] Registration limit value:', newProps.registrationLimit, 'type:', typeof newProps.registrationLimit);

          // IMPORTANT: Keep the original value without any conversion
          // This preserves the exact number the user entered
          const originalValue = newProps.registrationLimit;

          // If it's a string (which it shouldn't be at this point), we'll convert it
          // But we won't convert numbers to avoid any precision issues
          if (typeof originalValue === 'string') {
            console.log('[EventCategoryRepository] Converting string value to number');
            newProps.registrationLimit = Number(originalValue);
          }

          console.log('[EventCategoryRepository] Final registration limit value:', newProps.registrationLimit);
        }

        if (newProps.registrationCount !== undefined) newProps.registrationCount = Number(newProps.registrationCount);
        if (newProps.bibStartNumber !== undefined) newProps.bibStartNumber = Number(newProps.bibStartNumber);

        // Handle time values - ensure they're stored as time strings (HH:MM format)
        if (newProps.startTime) {
          // Make sure it's just the time part (HH:MM)
          const timeMatch = newProps.startTime.match(/(\d{1,2}):(\d{2})/);
          if (timeMatch) {
            newProps.startTime = timeMatch[0];
          }
        }

        // Convert boolean values to booleans
        if (newProps.bibRequireGeneration !== undefined) newProps.bibRequireGeneration = Boolean(newProps.bibRequireGeneration);
        if (newProps.registrationOpen !== undefined) newProps.registrationOpen = Boolean(newProps.registrationOpen);

        // Merge the new properties with existing ones
        dbCategory.properties = {
          ...existingProps,
          ...newProps
        };

        // Log the merged properties
        console.log('[EventCategoryRepository] Merged properties:', {
          id,
          name: existing.name,
          properties: dbCategory.properties
        });
      }
    }

    // Always update the updated_at timestamp
    dbCategory.updated_at = new Date().toISOString();

    const result = await this.update(id, dbCategory);

    if (!result.success) {
      throw new Error(`Failed to update event category: ${result.message}`);
    }

    return this.toEventCategory(result.data!);
  }

  /**
   * Delete a category
   */
  async deleteCategory(id: string): Promise<void> {
    const result = await this.delete(id);

    if (!result.success) {
      throw new Error(`Failed to delete event category: ${result.message}`);
    }
  }

  /**
   * Increment registration count for a category
   */
  async incrementRegistrationCount(id: string): Promise<EventCategory> {
    // First get the current category
    const category = await this.getCategoryById(id);

    if (!category) {
      throw new Error(`Category not found with id: ${id}`);
    }

    // Increment count and update
    const currentCount = category.properties?.registrationCount || 0;
    const updatedCount = currentCount + 1;

    // Update the properties field
    return this.updateCategory(id, {
      properties: {
        ...(category.properties || {}),
        registrationCount: updatedCount
      }
    });
  }

  /**
   * Check if a category has available spots
   */
  async hasAvailableSpots(id: string): Promise<boolean> {
    const category = await this.getCategoryById(id);

    if (!category) {
      throw new Error(`Category not found with id: ${id}`);
    }

    // Get values from properties
    const registrationLimit = category.properties?.registrationLimit;
    const registrationCount = category.properties?.registrationCount || 0;

    // If there's no limit, always return true
    if (!registrationLimit) {
      return true;
    }

    return registrationCount < registrationLimit;
  }

  /**
   * Get categories by event type-specific property
   * This demonstrates how to query JSON properties
   */
  async getCategoriesByProperty(
    eventId: string,
    propertyName: string,
    propertyValue: unknown): Promise<EventCategory[]> {
    try {
      const supabase = await createClient();

      // Query using the JSON property operator
      const { data, error } = await supabase
        .from('event_categories')
        .select('*')
        .eq('event_id', eventId)
        .eq(`properties->>${propertyName}`, propertyValue);

      if (error) {
        throw new Error(`Failed to query categories by property: ${error.message}`);
      }

      // Cast the data to the correct type before mapping
      const typedData = (data || []) as EventCategoryDB[];
      return typedData.map((item) => this.toEventCategory(item));
    } catch (error) {
      console.error('Error in getCategoriesByProperty:', error);
      throw error;
    }
  }
}