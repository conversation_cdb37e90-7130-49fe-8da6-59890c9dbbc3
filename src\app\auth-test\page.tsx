'use client'

import { useAuth } from '@/contexts/auth-context'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { UserAvatarMenu } from '@/components/layout/user-avatar-menu'
import { logger } from '@/lib/logger';

export default function AuthTestPage() {
  const { isSignedIn, user, loading, refreshSession } = useAuth()
  const [refreshCount, setRefreshCount] = useState(0)

  // Force a refresh when the component mounts
  useEffect(() => {
    logger.info('AuthTestPage mounted, refreshing session');
    refreshSession()
  }, [refreshSession])

  // Log auth state changes
  useEffect(() => {
    console.log('AuthTestPage auth state:', {
      isSignedIn,
      hasUser: !!user,
      loading,
      userData: user
    })
  }, [isSignedIn, user, loading])

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Auth Test Page</h1>
      
      <div className="bg-gray-100 p-6 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-4">Auth Context State</h2>
        <div className="space-y-2">
          <p><strong>Is Signed In:</strong> {isSignedIn ? 'Yes' : 'No'}</p>
          <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
          <p><strong>Has User:</strong> {user ? 'Yes' : 'No'}</p>
          
          {user && (
            <div className="mt-4">
              <h3 className="text-lg font-medium mb-2">User Data</h3>
              <pre className="bg-gray-200 p-4 rounded overflow-auto text-xs">
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>
          )}
        </div>
        
        <Button 
          onClick={() => {
            logger.info('Manual refresh triggered');
            refreshSession()
            setRefreshCount(prev => prev + 1)
          }}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Refresh Session
        </Button>
      </div>

      <div className="bg-gray-100 p-6 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-4">Direct UserAvatarMenu Test</h2>
        <div className="flex items-center space-x-4">
          {isSignedIn && user ? (
            <>
              <p>User Avatar Menu:</p>
              <UserAvatarMenu user={user} loading={loading} />
            </>
          ) : (
            <p>Not signed in or no user data available</p>
          )}
        </div>
      </div>

      <div className="bg-gray-100 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Manual User Object Test</h2>
        <div className="flex items-center space-x-4">
          <Button 
            onClick={() => {
              logger.info('Creating test user object');
              const testUser = {
                id: 'test-id',
                auth_user_id: 'test-auth-id',
                first_name: 'Test',
                last_name: 'User',
                email: '<EMAIL>',
                avatar: null
              }
              logger.info('Test user:', testUser);
            }}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Create Test User
          </Button>
        </div>
      </div>
    </div>
  )
}
