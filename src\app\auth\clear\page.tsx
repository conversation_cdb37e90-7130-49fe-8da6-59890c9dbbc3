'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { CheckCircle, AlertCircle, Trash2, RefreshCw, LogOut } from 'lucide-react';

export default function ClearAuthPage() {
  const [isClearing, setIsClearing] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string; details?: unknown } | null>(null);

  const handleClearSession = async () => {
    try {
      setIsClearing(true);
      setResult(null);

      // Clear local storage
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('sb-') || key.includes('supabase')) {
          localStorage.removeItem(key);
        }
      });

      // Clear session storage
      Object.keys(sessionStorage).forEach(key => {
        if (key.startsWith('sb-') || key.includes('supabase')) {
          sessionStorage.removeItem(key);
        }
      });

      // Call the server-side API to clear cookies
      const response = await fetch('/api/auth/clear-session', {
        method: 'GET',
        credentials: 'include', // Important for cookies
      });

      const data = await response.json();

      setResult({
        success: data.success,
        message: data.success ? 'Session cleared successfully' : 'Failed to clear session',
        details: data
      });
    } catch (error) {
      console.error('Error clearing session:', error);
      setResult({
        success: false,
        message: 'An error occurred while clearing session',
        details: error instanceof Error ? error.message : String(error)
      });
    } finally {
      setIsClearing(false);
    }
  };

  const handleResetState = async () => {
    try {
      setIsClearing(true);
      setResult(null);

      // Call the reset-state API
      const response = await fetch('/api/auth/reset-state', {
        method: 'GET',
        credentials: 'include', // Important for cookies
      });

      const data = await response.json();

      setResult({
        success: data.success,
        message: data.success ? 'Authentication state reset successfully' : 'Failed to reset authentication state',
        details: data
      });
    } catch (error) {
      console.error('Error resetting auth state:', error);
      setResult({
        success: false,
        message: 'An error occurred while resetting authentication state',
        details: error instanceof Error ? error.message : String(error)
      });
    } finally {
      setIsClearing(false);
    }
  };

  const handleSignOut = async () => {
    try {
      setIsClearing(true);
      setResult(null);

      // Call the sign-out API with GET method
      const response = await fetch('/api/auth/sign-out', {
        method: 'GET',
        credentials: 'include', // Important for cookies
      });

      const data = await response.json();

      setResult({
        success: data.success,
        message: data.success ? 'Signed out successfully' : 'Failed to sign out',
        details: data
      });
    } catch (error) {
      console.error('Error signing out:', error);
      setResult({
        success: false,
        message: 'An error occurred while signing out',
        details: error instanceof Error ? error.message : String(error)
      });
    } finally {
      setIsClearing(false);
    }
  };

  const handleSignIn = () => {
    window.location.href = '/sign-in?reset_auth=true';
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Authentication Tools</CardTitle>
          <CardDescription>
            Use these tools to manage your authentication state if you&apos;re experiencing issues.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {result && (
            <Alert variant={result.success ? "default" : "destructive"}>
              <div className="flex items-center gap-2">
                {result.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                <AlertTitle>{result.success ? 'Success' : 'Error'}</AlertTitle>
              </div>
              <AlertDescription className="mt-2">
                {result.message}
                {result.details && (
                  <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                )}
              </AlertDescription>
            </Alert>
          )}

          <div className="text-sm text-muted-foreground">
            <p>Choose an action:</p>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li><strong>Clear Session</strong>: Removes session cookies only</li>
              <li><strong>Reset Auth State</strong>: Clears all authentication data</li>
              <li><strong>Sign Out</strong>: Properly signs out from Supabase</li>
            </ul>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-2">
          <Button
            onClick={handleClearSession}
            disabled={isClearing}
            variant="outline"
            className="w-full"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {isClearing ? 'Clearing...' : 'Clear Session'}
          </Button>
          <Button
            onClick={handleResetState}
            disabled={isClearing}
            variant="outline"
            className="w-full"
          />
            <RefreshCw className="mr-2 h-4 w-4" />
            {isClearing ? 'Resetting...' : 'Reset Auth State'}
          </Button>
          <Button
            onClick={handleSignOut}
            disabled={isClearing}
            variant="outline"
            className="w-full"
          />
            <LogOut className="mr-2 h-4 w-4" />
            {isClearing ? 'Signing Out...' : 'Sign Out'}
          </Button>
          <Button
            onClick={handleSignIn}
            className="w-full"
          >
            Go to Sign In
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
