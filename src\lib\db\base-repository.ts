import { createClient, createAdminClient } from '@/lib/supabase/pages-client';
import { z } from 'zod';
import { SchemaManager } from './schema-manager';

/**
 * Options for repository operations
 */
export interface RepositoryOptions {
  /** Use admin client instead of regular client */
  useAdmin?: boolean;
  /** Maximum number of retry attempts for operations */
  maxRetries?: number;
  /** Whether to refresh schema cache on error */
  refreshCacheOnError?: boolean;
}

/**
 * Result of a repository operation
 */
export interface RepositoryResult<T> {
  /** Whether the operation was successful */
  success: boolean;
  /** Data returned by the operation */
  data?: T;
  /** Error if operation failed */
  error?: any;
  /** Error message if operation failed */
  message?: string;
  /** Whether a retry was attempted */
  retried?: boolean;
  /** Whether schema cache was refreshed */
  cacheRefreshed?: boolean;
}

// Define operation result type to fix type issues
type OperationResult<T> =
  | ({ success: true; data?: T; retried?: boolean; shouldRetry?: false; cacheRefreshed?: boolean })
  | ({ success: false; error: unknown; message: string; retried?: boolean; shouldRetry?: false; cacheRefreshed?: boolean; data?: T })
  | ({ shouldRetry: true; cacheRefreshed?: boolean; error: unknown });

/**
 * Base repository for database operations with schema validation, retry, and error handling
 */
export class BaseRepository<T extends Record<string, unknown>> {
  private readonly defaultOptions: RepositoryOptions = {
    useAdmin: false,
    maxRetries: 1,
    refreshCacheOnError: true,
  };

  // Create a partial schema for update operations
  private readonly partialSchema: z.ZodType<Partial<T>>;

  /**
   * Create a new repository
   * @param tableName Database table name
   * @param schema Zod schema for validation
   */
  constructor(
    private readonly tableName: string,
    private readonly schema: z.ZodType<T>
  ) {
    // Initialize the partial schema for updates
    // This is a safe way to handle partial validation without type casting
    this.partialSchema = z.object({
      // Allow any fields from the original schema to be optional
      ...Object.entries(this.schema instanceof z.ZodObject
        ? this.schema.shape
        : {}).reduce((acc, [key, validator]) => ({
          ...acc,
          [key]: z.optional(validator as z.ZodTypeAny)
        }), {})
    }) as z.ZodType<Partial<T>>;
  }

  /**
   * Create a new record
   */
  async create(data: T, options?: RepositoryOptions): Promise<RepositoryResult<T>> {
    // Log the create operation
    console.log(`[BaseRepository] Creating record in ${this.tableName}:`, {
      tableName: this.tableName,
      dataKeys: Object.keys(data),
      hasId: 'id' in data,
    });

    // Validate data against schema
    try {
      this.schema.parse(data);
    } catch (error) {
      console.error(`[BaseRepository] Validation failed for ${this.tableName}:`, error);

      // Log more detailed validation errors if it's a Zod error
      if (error instanceof z.ZodError) {
        error.errors.forEach((err, index) => {
          console.error(`[BaseRepository] Validation error ${index + 1}:`, {
            path: err.path.join('.'),
            message: err.message,
            code: err.code
          });
        });
      }

      return {
        success: false,
        error,
        message: 'Validation failed',
      };
    }

    return this.executeWithRetry<T>(async (supabase, attempt) => {
      console.log(`[BaseRepository] Executing insert on ${this.tableName}, attempt ${attempt + 1}`);

      const { data: result, error } = await supabase
        .from(this.tableName)
        .insert(data)
        .select()
        .single();

      if (error) {
        console.error(`[BaseRepository] Error inserting into ${this.tableName}:`, error);

        // Check if this might be a schema cache issue
        const isSchemaError =
          error.message.includes('column') &&
          (error.message.includes('does not exist') ||
            error.message.includes('undefined field'));

        if (isSchemaError && options?.refreshCacheOnError) {
          console.log(`[BaseRepository] Refreshing schema cache for ${this.tableName}`);
          await SchemaManager.refreshSchemaCache();
          return { shouldRetry: true as const, cacheRefreshed: true, error };
        }

        return {
          success: false as const,
          error,
          message: `Create operation failed: ${error.message}`,
          retried: attempt > 0,
        };
      }

      console.log(`[BaseRepository] Successfully created record in ${this.tableName}`);
      return {
        success: true as const,
        data: result as T,
        retried: attempt > 0,
      };
    }, options);
  }

  /**
   * Find records
   */
  async find(
    query: { [key: string]: unknown } = {},
    options?: RepositoryOptions
  ): Promise<RepositoryResult<T[]>> {
    return this.executeWithRetry<T[]>(async (supabase, attempt) => {
      let queryBuilder = supabase.from(this.tableName).select('*');

      // Apply filters
      Object.entries(query).forEach(([key, value]) => {
        queryBuilder = queryBuilder.eq(key, value);
      });

      const { data, error } = await queryBuilder;

      if (error) {
        // Check if this might be a schema cache issue
        const isSchemaError =
          error.message.includes('column') &&
          (error.message.includes('does not exist') ||
            error.message.includes('undefined field'));

        if (isSchemaError && options?.refreshCacheOnError) {
          await SchemaManager.refreshSchemaCache();
          return { shouldRetry: true as const, cacheRefreshed: true, error };
        }

        return {
          success: false as const,
          error,
          message: `Find operation failed: ${error.message}`,
          retried: attempt > 0,
        };
      }

      return {
        success: true as const,
        data: data as T[],
        retried: attempt > 0,
      };
    }, options);
  }

  /**
   * Find by ID
   */
  async findById(id: string, options?: RepositoryOptions): Promise<RepositoryResult<T>> {
    return this.executeWithRetry<T>(async (supabase, attempt) => {
      const { data, error } = await supabase
        .from(this.tableName)
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        // Not found is not a schema error
        if (error.code === 'PGRST116') {
          return {
            success: false as const,
            error,
            message: `Record not found with id: ${id}`,
            retried: attempt > 0,
          };
        }

        // Check if this might be a schema cache issue
        const isSchemaError =
          error.message.includes('column') &&
          (error.message.includes('does not exist') ||
            error.message.includes('undefined field'));

        if (isSchemaError && options?.refreshCacheOnError) {
          await SchemaManager.refreshSchemaCache();
          return { shouldRetry: true as const, cacheRefreshed: true, error };
        }

        return {
          success: false as const,
          error,
          message: `FindById operation failed: ${error.message}`,
          retried: attempt > 0,
        };
      }

      return {
        success: true as const,
        data: data as T,
        retried: attempt > 0,
      };
    }, options);
  }

  /**
   * Update a record
   */
  async update(id: string, data: Partial<T>, options?: RepositoryOptions): Promise<RepositoryResult<T>> {
    // Validate the partial data against schema
    try {
      // Use the partial schema for validation
      if (this.schema instanceof z.ZodObject) {
        this.schema.partial().parse(data);
      } else {
        // Fallback to our manually created partial schema
        this.partialSchema.parse(data);
      }
    } catch (error) {
      return {
        success: false,
        error,
        message: 'Validation failed',
      };
    }

    return this.executeWithRetry<T>(async (supabase, attempt) => {
      const { data: result, error } = await supabase
        .from(this.tableName)
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        // Check if this might be a schema cache issue
        const isSchemaError =
          error.message.includes('column') &&
          (error.message.includes('does not exist') ||
            error.message.includes('undefined field'));

        if (isSchemaError && options?.refreshCacheOnError) {
          await SchemaManager.refreshSchemaCache();
          return { shouldRetry: true as const, cacheRefreshed: true, error };
        }

        return {
          success: false as const,
          error,
          message: `Update operation failed: ${error.message}`,
          retried: attempt > 0,
        };
      }

      return {
        success: true as const,
        data: result as T,
        retried: attempt > 0,
      };
    }, options);
  }

  /**
   * Delete a record
   */
  async delete(id: string, options?: RepositoryOptions): Promise<RepositoryResult<void>> {
    return this.executeWithRetry<void>(async (supabase, attempt) => {
      const { error } = await supabase
        .from(this.tableName)
        .delete()
        .eq('id', id);

      if (error) {
        // Check if this might be a schema cache issue
        const isSchemaError =
          error.message.includes('column') &&
          (error.message.includes('does not exist') ||
            error.message.includes('undefined field'));

        if (isSchemaError && options?.refreshCacheOnError) {
          await SchemaManager.refreshSchemaCache();
          return { shouldRetry: true as const, cacheRefreshed: true, error };
        }

        return {
          success: false as const,
          error,
          message: `Delete operation failed: ${error.message}`,
          retried: attempt > 0,
        };
      }

      return {
        success: true as const,
        retried: attempt > 0,
      };
    }, options);
  }

  /**
   * Execute a database operation with retry and error handling
   */
  private async executeWithRetry<D>(
    operation: (
      supabase: unknown,
      attempt: number
    ) => Promise<OperationResult<D>>,
    options?: RepositoryOptions
  ): Promise<RepositoryResult<D>> {
    const opts = { ...this.defaultOptions, ...options };
    const maxRetries = opts.maxRetries || 1;

    // Get Supabase client based on options
    const getClient = async () => {
      try {
        if (opts.useAdmin) {
          return await createAdminClient();
        } else {
          return await createClient();
        }
      } catch (error) {
        console.error('Error creating Supabase client:', error);
        throw error;
      }
    };

    let attempt = 0;
    let lastResult: OperationResult<D> | null = null;
    let cacheRefreshed = false;

    while (attempt <= maxRetries) {
      try {
        // Get Supabase client
        const supabase = await getClient();
        console.log(`[BaseRepository] Executing operation on ${this.tableName}, attempt ${attempt + 1}/${maxRetries + 1}`);

        // Execute operation
        const result = await operation(supabase, attempt);

        // Update cache refreshed flag
        if (result.cacheRefreshed) {
          cacheRefreshed = true;
        }

        // Return result or retry if needed
        if ('shouldRetry' in result && result.shouldRetry) {
          lastResult = result;
          attempt++;
          continue;
        }

        // Include whether cache was refreshed in result
        if (result.success) {
          return {
            ...result,
            cacheRefreshed,
          };
        } else {
          return {
            ...result,
            cacheRefreshed,
          };
        }
      } catch (error) {
        console.error(`[BaseRepository] Error in ${this.tableName} operation (attempt ${attempt + 1}/${maxRetries + 1}):`, error);

        // This is the last attempt, return error
        if (attempt >= maxRetries) {
          return {
            success: false,
            error,
            message: error instanceof Error ? error.message : String(error),
            retried: attempt > 0,
            cacheRefreshed,
          };
        }

        // Try again
        lastResult = {
          shouldRetry: true as const,
          error,
        };
        attempt++;
      }
    }

    // Should never reach here, but just in case
    return {
      success: false,
      error: lastResult?.error || new Error('Unknown error'),
      message: lastResult?.error instanceof Error
        ? lastResult.error.message
        : 'Unknown error occurred after all retry attempts',
      retried: true,
      cacheRefreshed,
    };
  }
}