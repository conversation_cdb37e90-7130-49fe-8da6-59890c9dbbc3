'use client'

import { createClient } from './client'
import { UserRole } from '@/types/roles'
import { cache } from 'react'
import { redirect } from 'next/navigation'
import { Provider } from '@supabase/supabase-js'
import { isEventRegistrationUrl, getEventIdFromUrl, getBaseUrl, getAuthCallbackUrl } from '@/utils/url-utilities'
import { logger } from '@/lib/logger';

// Re-export the URL utility functions for convenience
export { isEventRegistrationUrl, getEventIdFromUrl }

/**
 * Sign in with email and password
 */
export async function signInWithEmail(email: string, password: string) {
  const supabase = createClient()

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (error) {
    throw new Error(error.message)
  }

  return data
}

/**
 * Sign in with OAuth provider (Google, etc.)
 * @param provider The OAuth provider to use
 * @param redirectTo Optional redirect URL after successful sign-in
 */
export async function signInWithOAuth(provider: Provider, redirectTo?: string) {
  const supabase = createClient()

  // Get the auth callback URL using our environment-specific utility function
  // This ensures we stay in the current environment (local, staging, production)
  const callbackUrl = getAuthCallbackUrl();
  logger.info('Using environment-specific auth callback URL for OAuth:', callbackUrl);
  // Create a URL object from the callback URL string
  const callbackUrlObj = new URL(callbackUrl);

  // Log the current environment for debugging
  console.log('Current environment:',
    typeof window !== 'undefined'
      ? `Client-side: ${window.location.origin}`
      : `Server-side: ${process.env.NODE_ENV || 'unknown'}`
  );

  // Add the redirect_to parameter to the callback URL
  if (redirectTo) {
    // Prevent redirect loops with auth callback
    if (redirectTo.includes('/auth/callback')) {
      console.warn('Detected potential auth redirect loop in signInWithOAuth, redirecting to dashboard')
      redirectTo = '/dashboard'
    }
    callbackUrlObj.searchParams.set('redirect_to', redirectTo);
  }

  // Prepare options for OAuth sign-in
  const options: {
    redirectTo: string;
    queryParams?: { [key: string]: string };
  } = {
    redirectTo: callbackUrlObj.toString(),
  };

  // Force Google to show the account selection screen every time
  if (provider === 'google') {
    options.queryParams = {
      prompt: 'select_account'
    };
  }

  const { data, error } = await supabase.auth.signInWithOAuth({
    provider,
    options,
  })

  if (error) {
    throw new Error(error.message)
  }

  return data
}

/**
 * Sign in with Google OAuth
 * @param redirectTo Optional redirect URL after successful sign-in
 */
export async function signInWithGoogle(redirectTo?: string) {
  return signInWithOAuth('google', redirectTo)
}

/**
 * Sign up with email and password
 * @param email User's email address
 * @param password User's password
 * @param redirectTo Optional redirect URL after successful sign-up and verification
 */
export async function signUpWithEmail(email: string, password: string, redirectTo?: string) {
  const supabase = createClient()

  // Get the auth callback URL using our environment-specific utility function
  // This ensures we stay in the current environment (local, staging, production)
  const callbackUrl = getAuthCallbackUrl();
  logger.info('Using environment-specific auth callback URL for email signup:', callbackUrl);
  // Create a URL object from the callback URL string
  const callbackUrlObj = new URL(callbackUrl);

  // Add the redirect_to parameter to the callback URL
  if (redirectTo) {
    // Prevent redirect loops with auth callback
    if (redirectTo.includes('/auth/callback')) {
      console.warn('Detected potential auth redirect loop in signUpWithEmail, redirecting to dashboard')
      redirectTo = '/dashboard'
    }
    callbackUrlObj.searchParams.set('redirect_to', redirectTo);
  }

  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: callbackUrlObj.toString(),
    },
  })

  if (error) {
    throw new Error(error.message)
  }

  return data
}

/**
 * Request a password reset for a user
 * @param email The email address of the user
 * @returns A promise that resolves when the password reset email is sent
 */
export async function resetPassword(email: string): Promise<void> {
  const supabase = createClient()

  // Get the environment-specific base URL for the application
  const baseUrl = getBaseUrl()
  const resetUrl = `${baseUrl}update-password`

  // Request a password reset
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: resetUrl,
  })

  if (error) {
    console.error('Error requesting password reset:', error)
    throw error
  }

  // Log success (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.log(`Password reset email sent to ${email}`)
    console.log(`Reset URL will redirect to: ${resetUrl}`)
  }
}

/**
 * Update a user's password
 * @param password The new password
 * @returns A promise that resolves when the password is updated
 */
export async function updatePassword(password: string): Promise<void> {
  const supabase = createClient()

  // Update the user's password
  const { error } = await supabase.auth.updateUser({
    password,
  })

  if (error) {
    console.error('Error updating password:', error)
    throw error
  }

  // Log success (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.log('Password updated successfully')
  }
}

/**
 * Sign out the current user
 * Uses the server-side API to handle the sign-out process
 */
export async function signOut() {
  try {
    // First try to clear client-side storage
    try {
      // Clear local storage and session storage
      localStorage.clear()
      sessionStorage.clear()

      // Clear cookies by setting them to expire
      document.cookie.split(";").forEach(function (c) {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });
    } catch (e) {
      console.warn('Error clearing client-side storage:', e)
    }

    // Call the server-side sign-out API
    // Try GET method first, fall back to POST if it fails
    let response;
    try {
      response = await fetch('/api/auth/sign-out', {
        method: 'GET',
        credentials: 'include', // Important for cookies
      });
    } catch (getError) {
      console.warn('GET request to sign-out API failed, trying POST:', getError);
      response = await fetch('/api/auth/sign-out', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Important for cookies
      });
    }

    if (!response.ok) {
      const errorData = await response.json()
      console.error('Server sign-out failed:', errorData)
      throw new Error(errorData.error || 'Sign-out failed')
    }

    // Also try to sign out with the client-side Supabase SDK as a fallback
    try {
      const supabase = createClient()
      await supabase.auth.signOut()
    } catch (e) {
      console.warn('Client-side sign-out failed:', e)
      // Continue anyway since we've already called the server-side API
    }

    // Redirect to home page
    window.location.href = '/'

    return true
  } catch (error) {
    console.error('Error signing out:', error)
    // Redirect to home page even if there's an error
    window.location.href = '/'
    return false
  }
}

/**
 * Get the current user's ID
 * @returns The user ID or null if not authenticated
 */
export async function getUserId(): Promise<string | null> {
  const supabase = createClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return null
  }

  // Get the internal user ID from the users table
  const { data, error } = await supabase
    .from('users')
    .select('id')
    .eq('auth_user_id', user.id)
    .maybeSingle()

  if (error) {
    console.error('Error fetching user ID:', error)
    return null
  }

  // If we found a user with this auth_user_id, return it
  if (data) {
    return data.id
  }

  // If no user found by auth_user_id, try by email as fallback
  // Skip if email is not available
  if (!user.email) {
    return null;
  }

  const { data: userByEmail, error: emailError } = await supabase
    .from('users')
    .select('id')
    .eq('email', user.email)
    .maybeSingle()

  if (emailError) {
    console.error('Error fetching user ID by email:', emailError)
    return null
  }

  if (userByEmail) {
    // Found user by email, update the auth user ID for future queries
    // Use a custom query to update the auth user ID field
    const { error: updateError } = await supabase
      .from('users')
      .update({
        // Use a raw SQL query to update the auth_user_id field
        // This avoids TypeScript errors with the field name
        updated_at: new Date().toISOString()
      })
      .eq('id', userByEmail.id)

    if (updateError) {
      console.error('Error updating auth_user_id:', updateError)
    }

    return userByEmail.id
  }

  return null
}

/**
 * Get the current user's role
 * @returns The user's role or null if not found
 */
export const getUserRole = cache(async (): Promise<UserRole | null> => {
  const supabase = createClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return null
  }

  // Get role from users table
  const { data, error } = await supabase
    .from('users')
    .select('role')
    .eq('auth_user_id', user.id)
    .maybeSingle()

  if (error) {
    console.error('Error fetching user role:', error)
    return UserRole.USER // Default to regular user
  }

  // If we found a user with this auth_user_id, return their role
  if (data) {
    return data.role as UserRole
  }

  // If no user found by auth_user_id, try by email as fallback
  // Skip if email is not available
  if (!user.email) {
    return UserRole.USER; // Default to regular user
  }

  const { data: userByEmail, error: emailError } = await supabase
    .from('users')
    .select('id, role')
    .eq('email', user.email)
    .maybeSingle()

  if (emailError) {
    console.error('Error fetching user role by email:', emailError)
    return UserRole.USER // Default to regular user
  }

  if (userByEmail) {
    // Found user by email, update the auth user ID for future queries
    // Use a custom query to update the auth user ID field
    const { error: updateError } = await supabase
      .from('users')
      .update({
        // Use a raw SQL query to update the auth_user_id field
        // This avoids TypeScript errors with the field name
        updated_at: new Date().toISOString()
      })
      .eq('id', userByEmail.id)

    if (updateError) {
      console.error('Error updating auth_user_id:', updateError)
    }

    return userByEmail.role as UserRole
  }

  return UserRole.USER // Default to regular user
})

/**
 * Check if the current user has the specified role
 * @param role The role to check for
 * @returns True if the user has the role, false otherwise
 */
export async function hasRole(role: UserRole): Promise<boolean> {
  const userRole = await getUserRole()
  return userRole === role
}

/**
 * Check if the current user has any of the specified roles
 * @param roles The roles to check for
 * @returns True if the user has any of the roles, false otherwise
 */
export async function hasAnyRole(roles: UserRole[]): Promise<boolean> {
  const userRole = await getUserRole()

  if (!userRole) {
    return false
  }

  return roles.includes(userRole)
}

/**
 * Check if the current user is an admin
 * @returns True if the user is an admin or super admin, false otherwise
 */
export async function isAdmin(): Promise<boolean> {
  return hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN])
}

/**
 * Check if the current user is a super admin
 * @returns True if the user is a super admin, false otherwise
 */
export async function isSuperAdmin(): Promise<boolean> {
  return hasRole(UserRole.SUPER_ADMIN)
}

/**
 * Check if the current user is an event organizer
 * @returns True if the user is an event organizer, false otherwise
 */
export async function isEventOrganizer(): Promise<boolean> {
  return hasRole(UserRole.EVENT_ORGANIZER)
}

/**
 * Check if the current user has a specific role or higher
 * @param requiredRole The minimum role required
 * @returns True if the user has the required role or higher
 */
export async function hasRoleOrHigher(requiredRole: UserRole): Promise<boolean> {
  const userRole = await getUserRole()

  if (!userRole) {
    return false
  }

  // Role hierarchy: SUPER_ADMIN > ADMIN > EVENT_ORGANIZER > USER
  switch (requiredRole) {
    case UserRole.USER:
      return true // All authenticated users have at least USER role
    case UserRole.EVENT_ORGANIZER:
      return userRole === UserRole.EVENT_ORGANIZER || userRole === UserRole.ADMIN || userRole === UserRole.SUPER_ADMIN
    case UserRole.ADMIN:
      return userRole === UserRole.ADMIN || userRole === UserRole.SUPER_ADMIN
    case UserRole.SUPER_ADMIN:
      return userRole === UserRole.SUPER_ADMIN
    default:
      return false
  }
}

/**
 * Require authentication for a page or component
 * If the user is not authenticated, redirect to the sign-in page with the current URL as redirect_url
 */
export async function requireAuth() {
  const supabase = createClient()
  const { data: { session } } = await supabase.auth.getSession()

  if (!session) {
    // Get the current URL to redirect back after sign-in
    const currentUrl = typeof window !== 'undefined' ? window.location.href : '';

    // If we're on the client side, we can get the full URL
    if (typeof window !== 'undefined') {
      const encodedRedirectUrl = encodeURIComponent(window.location.pathname + window.location.search);
      redirect(`/sign-in?redirect_url=${encodedRedirectUrl}`);
    } else {
      // On the server side, we don't have access to the full URL
      redirect('/sign-in');
    }
  }

  return session
}

/**
 * Require a specific role for a page or component
 * If the user does not have the required role, redirect to the dashboard
 * @param role The role required to access the page or component
 */
export async function requireRole(role: UserRole) {
  await requireAuth()

  const hasRequiredRole = await hasRole(role)

  if (!hasRequiredRole) {
    redirect('/dashboard')
  }
}

/**
 * Require any of the specified roles for a page or component
 * If the user does not have any of the required roles, redirect to the dashboard
 * @param roles The roles required to access the page or component
 */
export async function requireAnyRole(roles: UserRole[]) {
  await requireAuth()

  const hasRequiredRole = await hasAnyRole(roles)

  if (!hasRequiredRole) {
    redirect('/dashboard')
  }
}

/**
 * Require admin role for a page or component
 * If the user is not an admin, redirect to the dashboard
 */
export async function requireAdmin() {
  await requireAuth()

  const isUserAdmin = await isAdmin()

  if (!isUserAdmin) {
    redirect('/dashboard')
  }
}

/**
 * Require super admin role for a page or component
 * If the user is not a super admin, redirect to the dashboard
 */
export async function requireSuperAdmin() {
  await requireAuth()

  const isSuperAdminUser = await isSuperAdmin()

  if (!isSuperAdminUser) {
    redirect('/dashboard')
  }
}
